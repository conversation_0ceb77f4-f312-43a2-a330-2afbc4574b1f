<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>صلاح تريدنج - الربح الذكي في التداول</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Oi&display=swap" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700;900&display=swap');
        
        :root {
            --bg-primary: #0a0f1a;
            --bg-secondary: #111827;
            --accent-color: #00ffae;
            --accent-hover: #14ffec;
            --text-primary: #ffffff;
            --text-secondary: #b0b8d1;
            --highlight: #ffcc00;
            --highlight-red: #ff0055;
            --shadow: rgba(0,0,0,0.7);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Tajawal', sans-serif;
        }
        
        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            overflow-x: hidden;
        }
        
        /* ===== الهيدر الزجاجي ===== */
        header {
            position: sticky;
            top: 0;
            backdrop-filter: blur(15px);
            background: rgba(10, 15, 26, 0.9);
            border-bottom: 1px solid rgba(0, 255, 174, 0.3);
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            animation: fadeInDown 0.8s ease-out;
            padding: 15px 5%;
        }
        
        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--accent-color);
            text-shadow: 0 0 15px rgba(0, 255, 174, 0.7);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .logo-icon {
            animation: pulse 2s infinite;
        }
        
        .nav-links {
            display: flex;
            gap: 30px;
        }
        
        .nav-links a {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.1rem;
            position: relative;
            transition: all 0.3s ease;
            padding: 5px 0;
        }
        
        .nav-links a:hover {
            color: var(--accent-color);
            transform: translateY(-3px);
        }
        
        .nav-links a::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: var(--accent-color);
            transition: width 0.3s ease;
        }
        
        .nav-links a:hover::after {
            width: 100%;
        }
        
        .social-icons {
            display: flex;
            gap: 15px;
        }
        
        .social-icons a {
            color: var(--text-primary);
            font-size: 1.3rem;
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 255, 174, 0.1);
            border: 1px solid rgba(0, 255, 174, 0.3);
        }
        
        .social-icons a:hover {
            color: var(--accent-color);
            transform: scale(1.2) rotate(10deg);
            background: rgba(0, 255, 174, 0.2);
            box-shadow: 0 0 15px rgba(0, 255, 174, 0.3);
        }
        
        /* ===== القسم الرئيسي (Hero) ===== */
        .hero {
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            overflow: hidden;
        }
        
        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(rgba(10, 15, 26, 0.7), rgba(10, 15, 26, 0.9));
            z-index: 1;
        }
        
        .hero video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            z-index: 0;
        }
        
        .hero-content {
            position: relative;
            z-index: 2;
            max-width: 800px;
            padding: 0 20px;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            text-shadow: 0 0 20px rgba(0, 255, 174, 0.7);
            animation: textGlow 3s infinite alternate;
        }
        
        @keyframes textGlow {
            from {
                text-shadow: 0 0 10px rgba(0, 255, 174, 0.5);
            }
            to {
                text-shadow: 0 0 20px rgba(0, 255, 174, 0.9), 0 0 30px rgba(0, 255, 174, 0.5);
            }
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 30px;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(45deg, var(--accent-color), var(--accent-hover));
            color: var(--bg-primary);
            padding: 15px 40px;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            text-decoration: none;
            box-shadow: 0 5px 25px rgba(0, 255, 174, 0.5);
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
            position: relative;
            overflow: hidden;
            border: none;
            cursor: pointer;
        }
        
        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 35px rgba(0, 255, 174, 0.7);
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                to bottom right,
                transparent,
                transparent,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent,
                transparent,
                transparent
            );
            transform: rotate(30deg);
            animation: shine 3s infinite;
        }
        
        @keyframes shine {
            0% {
                left: -50%;
            }
            100% {
                left: 150%;
            }
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(0, 255, 174, 0.7);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(0, 255, 174, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(0, 255, 174, 0);
            }
        }
        
        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 50px;
            flex-wrap: wrap;
        }
        
        .stat-item {
            background: rgba(0, 255, 174, 0.1);
            border: 1px solid rgba(0, 255, 174, 0.3);
            border-radius: 15px;
            padding: 20px;
            min-width: 150px;
            backdrop-filter: blur(5px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--accent-color);
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }
        
        /* ===== قسم من نحن ===== */
        .about {
            padding: 100px 5%;
            background-color: var(--bg-secondary);
            position: relative;
            overflow: hidden;
        }
        
        .about::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 204, 0, 0.15) 0%, transparent 70%);
            z-index: 0;
        }
        
        .about-container {
            display: flex;
            align-items: center;
            gap: 50px;
            position: relative;
            z-index: 1;
        }
        
        .about-text {
            flex: 1;
        }
        
        .about h2 {
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: var(--highlight);
            position: relative;
            display: inline-block;
        }
        
        .about h2::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 0;
            width: 50%;
            height: 3px;
            background: linear-gradient(90deg, var(--highlight), transparent);
            border-radius: 3px;
        }
        
        .about p {
            font-size: 1.1rem;
            line-height: 1.8;
            margin-bottom: 20px;
            color: var(--text-secondary);
        }
        
        .about-image {
            flex: 1;
            position: relative;
            perspective: 1000px;
        }
        
        .chart-3d {
            width: 100%;
            height: 400px;
            background: rgba(0, 255, 174, 0.1);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 174, 0.3);
            transform-style: preserve-3d;
            transform: rotateX(10deg) rotateY(-10deg);
            transition: all 0.5s ease;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--accent-color);
            backdrop-filter: blur(5px);
        }
        
        .chart-3d:hover {
            transform: rotateX(5deg) rotateY(-5deg) translateY(-10px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.6);
        }
        
        /* ===== قسم النتائج والأرباح ===== */
        .results {
            padding: 100px 5%;
            background-color: var(--bg-primary);
            position: relative;
        }
        
        .results::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 80% 20%, rgba(255, 0, 85, 0.1) 0%, transparent 50%);
            z-index: 0;
        }
        
        .results-container {
            position: relative;
            z-index: 1;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 50px;
            color: var(--highlight);
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 3px;
        }
        
        .screenshots-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 40px;
        }
        
        .screenshot-card {
            background: rgba(17, 24, 39, 0.7);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 174, 0.2);
            transition: all 0.5s ease;
            transform: translateY(20px);
            opacity: 0;
        }
        
        .screenshot-card.visible {
            transform: translateY(0);
            opacity: 1;
        }
        
        .screenshot-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(0, 255, 174, 0.5);
        }
        
        .screenshot-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-bottom: 1px solid rgba(0, 255, 174, 0.2);
        }
        
        .screenshot-info {
            padding: 20px;
        }
        
        .screenshot-title {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: var(--accent-color);
        }
        
        .screenshot-meta {
            display: flex;
            justify-content: space-between;
            color: var(--text-secondary);
            font-size: 0.9rem;
        }
        
        .profit-badge {
            background: rgba(0, 255, 174, 0.1);
            color: var(--accent-color);
            padding: 3px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 700;
        }
        
        .view-more {
            text-align: center;
            margin-top: 40px;
        }
        
        .view-more-btn {
            display: inline-block;
            background: transparent;
            color: var(--accent-color);
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 700;
            text-decoration: none;
            border: 2px solid var(--accent-color);
            transition: all 0.3s ease;
        }
        
        .view-more-btn:hover {
            background: rgba(0, 255, 174, 0.1);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 255, 174, 0.3);
        }
        
        /* ===== قسم الإشارات ===== */
        .signals {
            padding: 100px 5%;
            background-color: var(--bg-secondary);
        }
        
        .pricing-cards {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }
        
        .pricing-card {
            background: rgba(17, 24, 39, 0.7);
            border-radius: 20px;
            padding: 40px 30px;
            width: 300px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 174, 0.2);
            transition: all 0.5s ease;
            transform: scale(0.95);
            opacity: 0;
            position: relative;
            overflow: hidden;
        }
        
        .pricing-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--accent-color), var(--highlight));
        }
        
        .pricing-card.visible {
            transform: scale(1);
            opacity: 1;
        }
        
        .pricing-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
            border-color: rgba(0, 255, 174, 0.5);
        }
        
        .pricing-card h3 {
            font-size: 1.8rem;
            margin-bottom: 20px;
            color: var(--accent-color);
        }
        
        .price {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--highlight);
        }
        
        .original-price {
            text-decoration: line-through;
            color: var(--text-secondary);
            font-size: 1.5rem;
            margin-left: 10px;
        }
        
        .pricing-features {
            list-style: none;
            margin-bottom: 30px;
        }
        
        .pricing-features li {
            margin-bottom: 10px;
            color: var(--text-secondary);
            position: relative;
            padding-right: 25px;
        }
        
        .pricing-features li::before {
            content: '✓';
            color: var(--accent-color);
            position: absolute;
            right: 0;
        }
        
        .pricing-button {
            display: block;
            text-align: center;
            background: linear-gradient(45deg, var(--accent-color), var(--accent-hover));
            color: var(--bg-primary);
            padding: 12px 0;
            border-radius: 50px;
            font-weight: 700;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .pricing-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 255, 174, 0.4);
        }
        
        /* ===== قسم العملاء ===== */
        .testimonials {
            padding: 100px 5%;
            background-color: var(--bg-primary);
            position: relative;
        }
        
        .testimonials::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 80%, rgba(255, 204, 0, 0.1) 0%, transparent 50%);
            z-index: 0;
        }
        
        .testimonial-slider {
            max-width: 1000px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .testimonial-slide {
            background: rgba(10, 15, 26, 0.7);
            border-radius: 20px;
            padding: 40px;
            margin: 0 20px;
            border: 1px solid rgba(0, 255, 174, 0.3);
            text-align: center;
            backdrop-filter: blur(5px);
        }
        
        .client-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 20px;
            border: 3px solid var(--accent-color);
            object-fit: cover;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        
        .client-rating {
            color: var(--highlight);
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .client-text {
            font-style: italic;
            color: var(--text-secondary);
            line-height: 1.8;
            margin-bottom: 20px;
            font-size: 1.1rem;
        }
        
        .client-name {
            font-weight: 700;
            color: var(--accent-color);
            font-size: 1.2rem;
        }
        
        /* ===== قسم الأسئلة الشائعة ===== */
        .faq {
            padding: 100px 5%;
            background-color: var(--bg-secondary);
        }
        
        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .faq-item {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(17, 24, 39, 0.7);
            border: 1px solid rgba(0, 255, 174, 0.2);
            transition: all 0.3s ease;
        }
        
        .faq-item:hover {
            border-color: rgba(0, 255, 174, 0.5);
        }
        
        .faq-question {
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 500;
            color: var(--accent-color);
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .faq-question::after {
            content: '+';
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .faq-item.active .faq-question::after {
            content: '-';
        }
        
        .faq-answer {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
            color: var(--text-secondary);
            line-height: 1.6;
        }
        
        .faq-item.active .faq-answer {
            padding: 0 20px 20px;
            max-height: 500px;
        }
        
        /* ===== قسم التواصل ===== */
        .contact {
            padding: 100px 5%;
            background-color: var(--bg-primary);
            position: relative;
        }
        
        .contact::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: radial-gradient(circle at 20% 50%, rgba(255, 0, 85, 0.1) 0%, transparent 50%);
            z-index: 0;
        }
        
        .contact-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(17, 24, 39, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(0, 255, 174, 0.3);
            position: relative;
            z-index: 1;
        }
        
        .contact h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: var(--highlight);
        }
        
        .contact-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .form-group label {
            font-weight: 500;
            color: var(--accent-color);
        }
        
        .form-group input,
        .form-group textarea {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            padding: 12px 15px;
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--accent-color);
            background: rgba(0, 255, 174, 0.05);
        }
        
        .submit-button {
            background: linear-gradient(45deg, var(--accent-color), var(--accent-hover));
            color: var(--bg-primary);
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-weight: 700;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }
        
        .submit-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 255, 174, 0.4);
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 40px;
        }
        
        .social-link {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-primary);
            text-decoration: none;
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }
        
        .social-link:hover {
            color: var(--accent-color);
            transform: translateY(-3px);
        }
        
        .social-icon {
            font-size: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .social-link:hover .social-icon {
            transform: rotate(15deg) scale(1.2);
        }
        
        /* ===== الفوتر ===== */
        footer {
            background-color: var(--bg-secondary);
            padding: 50px 5% 30px;
            border-top: 1px solid rgba(0, 255, 174, 0.2);
        }
        
        .footer-container {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .footer-col {
            flex: 1;
            min-width: 200px;
        }
        
        .footer-col h3 {
            color: var(--accent-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
            position: relative;
            padding-bottom: 10px;
        }
        
        .footer-col h3::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 50%;
            height: 2px;
            background: var(--accent-color);
        }
        
        .footer-links {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: 10px;
        }
        
        .footer-links a {
            color: var(--text-secondary);
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .footer-links a:hover {
            color: var(--accent-color);
            padding-right: 5px;
        }
        
        .copyright {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            padding-top: 30px;
            border-top: 1px solid rgba(0, 255, 174, 0.1);
        }
        
        /* ===== أنيميشن التمرير ===== */
        @keyframes zoomIn {
            from {
                transform: scale(0.95);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        /* ===== Responsive Design ===== */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .about-container {
                flex-direction: column;
            }
            
            .pricing-cards {
                flex-direction: column;
                align-items: center;
            }
            
            .contact-container {
                padding: 30px;
            }
            
            .footer-container {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <section class="hero" id="home" style="position: relative; height: 100vh; overflow: hidden; display: flex; align-items: center; justify-content: center; text-align: center; color: #fff; font-family: 'Tajawal', sans-serif;">
    <!-- الشريط الجانبي الجديد -->
    <div class="side-nav" style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); z-index: 10; background: rgba(0, 20, 50, 0.7); backdrop-filter: blur(10px); border-radius: 15px; padding: 20px 15px; border: 1px solid rgba(255,255,255,0.1); box-shadow: 0 5px 15px rgba(0,0,0,0.2);">
        <nav style="display: flex; flex-direction: column; gap: 15px;">
            <a href="#" style="color: white; text-decoration: none; padding: 10px 20px; border-radius: 8px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-home"></i> الرئيسية
            </a>
            <a href="#" style="color: white; text-decoration: none; padding: 10px 20px; border-radius: 8px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-users"></i> من نحن
            </a>
            
            <!-- قائمة الأدوات المنسدلة -->
            <div class="dropdown" style="position: relative;">
                <button style="background: transparent; border: none; color: white; width: 100%; text-align: right; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: space-between; gap: 10px;">
                    <span><i class="fas fa-tools"></i> الأدوات</span>
                    <i class="fas fa-chevron-down"></i>
                </button>
                <div class="dropdown-content" style="display: none; flex-direction: column; background: rgba(0, 30, 60, 0.9); border-radius: 8px; margin-top: 5px; overflow: hidden;">
                    <a href="#" style="color: white; text-decoration: none; padding: 10px 25px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-calculator"></i> حاسبة اللوت
                    </a>
                    <a href="#" style="color: white; text-decoration: none; padding: 10px 25px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-calendar-alt"></i> التقويم الاقتصادي
                    </a>
                    <a href="#" style="color: white; text-decoration: none; padding: 10px 25px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                        <i class="fas fa-chart-line"></i> خطط إدارة رأس المال
                    </a>
                </div>
            </div>
            
            <a href="#" style="color: white; text-decoration: none; padding: 10px 20px; border-radius: 8px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-handshake"></i> الشريك الرسمي
            </a>
            <a href="#" style="color: white; text-decoration: none; padding: 10px 20px; border-radius: 8px; transition: all 0.3s ease; display: flex; align-items: center; gap: 10px;">
                <i class="fas fa-envelope"></i> تواصل معنا
            </a>
        </nav>
    </div>

    <!-- الفيديو الخلفية -->
    <video autoplay muted loop style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; z-index: -1; opacity: 0.8;">
        <source src="Trading Background.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>
    
    <!-- الطبقة الشفافة -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(0,40,85,0.9) 0%, rgba(0,20,50,0.8) 100%); z-index: -1;"></div>
    
    <!-- المحتوى الرئيسي -->
    <div class="hero-content" style="max-width: 1200px; padding: 0 20px; z-index: 1;">
        <h1 style="font-size: 3.5rem; font-weight: 700; margin-bottom: 20px; text-shadow: 0 2px 10px rgba(0,0,0,0.3); animation: fadeInDown 1s ease;">
            أهلاً بكم في <span style="color: #4fc3f7;">صلاح تريدنج</span>
        </h1>
        
        <p style="font-size: 1.5rem; line-height: 1.6; margin-bottom: 30px; max-width: 800px; margin-left: auto; margin-right: auto; text-shadow: 0 1px 3px rgba(0,0,0,0.5); animation: fadeIn 1.5s ease;">
            نقدم لكم <strong style="color: #4fc3f7;">أدق الإشارات</strong> وأفضل <strong style="color: #4fc3f7;">استراتيجيات التداول</strong><br>
            مع معدل دقة يتجاوز 85% ومتابعة لحظية لتحقيق <strong style="color: #4fc3f7;">أعلى الأرباح</strong>
        </p>
        
        <a href="#signals" class="cta-button" style="display: inline-block; background: linear-gradient(45deg, #4fc3f7, #2a89d8); color: white; padding: 15px 40px; border-radius: 30px; font-size: 1.2rem; font-weight: 600; text-decoration: none; margin-bottom: 40px; box-shadow: 0 5px 15px rgba(42, 137, 216, 0.4); transition: all 0.3s ease; border: none; animation: pulse 2s infinite; text-shadow: none;">
            🔥 سجّل الآن وابدأ رحلة الربح الذكي!
        </a>
        
        <div class="stats" style="display: flex; justify-content: center; flex-wrap: wrap; gap: 30px; margin-top: 50px; animation: fadeInUp 1s ease;">
            <div class="stat-item" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 25px 35px; border-radius: 15px; border: 1px solid rgba(255,255,255,0.2); min-width: 180px; transition: transform 0.3s ease;">
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">85%</div>
                <div class="stat-label" style="font-size: 1rem; opacity: 0.9;">دقة الإشارات</div>
            </div>
            <div class="stat-item" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 25px 35px; border-radius: 15px; border: 1px solid rgba(255,255,255,0.2); min-width: 180px; transition: transform 0.3s ease;">
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">4000+</div>
                <div class="stat-label" style="font-size: 1rem; opacity: 0.9;">نقطة شهرياً</div>
            </div>
            <div class="stat-item" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 25px 35px; border-radius: 15px; border: 1px solid rgba(255,255,255,0.2); min-width: 180px; transition: transform 0.3s ease;">
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">500+</div>
                <div class="stat-label" style="font-size: 1rem; opacity: 0.9;">عميل راضي</div>
            </div>
            <div class="stat-item" style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); padding: 25px 35px; border-radius: 15px; border: 1px solid rgba(255,255,255,0.2); min-width: 180px; transition: transform 0.3s ease;">
                <div class="stat-number" style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">24/7</div>
                <div class="stat-label" style="font-size: 1rem; opacity: 0.9;">دعم فوري</div>
            </div>
        </div>
    </div>

    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes fadeInDown {
            from { 
                opacity: 0;
                transform: translateY(-30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes fadeInUp {
            from { 
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        @keyframes pulse {
            0% { transform: scale(1); box-shadow: 0 5px 15px rgba(42, 137, 216, 0.4); }
            50% { transform: scale(1.05); box-shadow: 0 10px 25px rgba(42, 137, 216, 0.6); }
            100% { transform: scale(1); box-shadow: 0 5px 15px rgba(42, 137, 216, 0.4); }
        }
        .stat-item:hover {
            transform: translateY(-10px);
            background: rgba(255,255,255,0.15) !important;
        }
        .cta-button:hover {
            background: linear-gradient(45deg, #2a89d8, #4fc3f7) !important;
            transform: translateY(-3px);
        }
        .side-nav a:hover, .dropdown button:hover {
            background: rgba(79, 195, 247, 0.3) !important;
        }
        .dropdown-content a:hover {
            background: rgba(79, 195, 247, 0.5) !important;
        }
        
        /* JavaScript للتعامل مع القائمة المنسدلة */
        document.addEventListener('DOMContentLoaded', function() {
            const dropdownBtn = document.querySelector('.dropdown button');
            const dropdownContent = document.querySelector('.dropdown-content');
            
            dropdownBtn.addEventListener('click', function() {
                if(dropdownContent.style.display === 'flex') {
                    dropdownContent.style.display = 'none';
                    this.querySelector('.fa-chevron-down').style.transform = 'rotate(0deg)';
                } else {
                    dropdownContent.style.display = 'flex';
                    this.querySelector('.fa-chevron-down').style.transform = 'rotate(180deg)';
                }
            });
        });
    </style>
</section>

<!-- رابط أيقونات Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <section id="about" style="position: relative; min-height: 100vh; overflow: hidden; display: flex; align-items: center; justify-content: center; color: #fff; font-family: 'Tajawal', sans-serif; background: linear-gradient(135deg, rgba(0,40,85,0.9) 0%, rgba(0,20,50,0.8) 100%);">

        <!-- فيديو خلفي (نفس فيديو الهيرو) -->
        <video autoplay muted loop style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; z-index: -1; opacity: 0.7;">
            <source src="Trading Background.mp4" type="video/mp4">
            Your browser does not support the video tag.
        </video>
    
        <!-- طبقة تظليل (نفس الهيرو) -->
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(0,40,85,0.8) 0%, rgba(0,20,50,0.7) 100%); z-index: -1;"></div>
    
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; z-index: 1; width: 100%;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 60px; align-items: center;">
    
                <!-- الجزء النصي -->
                <div style="position: relative;">
                    <h2 style="font-size: 2.8rem; font-weight: 700; margin-bottom: 30px; color: #4fc3f7; position: relative; display: inline-block;">
                        <span class="typing-title" style="position: relative;">من نحن</span>
                        <span style="position: absolute; bottom: -10px; right: 0; width: 100%; height: 3px; background: linear-gradient(90deg, transparent, #4fc3f7); animation: underlineGrow 1s ease-out forwards;"></span>
                    </h2>
    
                    <div class="typing-container" style="background: rgba(0,0,0,0.2); backdrop-filter: blur(5px); padding: 30px; border-radius: 15px; border: 1px solid rgba(255,255,255,0.1);">
                        <p id="typing-line1" style="font-size: 1.3rem; line-height: 1.8; margin-bottom: 25px; min-height: 60px; border-right: 2px solid #4fc3f7; padding-right: 15px; animation: blinkCursor 0.7s infinite;"></p>
                        <p id="typing-line2" style="font-size: 1.3rem; line-height: 1.8; margin-bottom: 25px; min-height: 60px;"></p>
                        <p id="typing-line3" style="font-size: 1.3rem; line-height: 1.8; min-height: 60px;"></p>
                    </div>
    
                    <div style="margin-top: 40px; display: flex; gap: 20px;">
                        <a href="#signals" class="cta-button" style="display: inline-block; background: linear-gradient(45deg, #4fc3f7, #2a89d8); color: white; padding: 15px 40px; border-radius: 30px; font-size: 1.1rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(42, 137, 216, 0.4);">
                            عرض الباقات
                        </a>
                        <a href="#contact" class="secondary-button" style="display: inline-block; background: transparent; color: #4fc3f7; padding: 15px 40px; border-radius: 30px; font-size: 1.1rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; border: 2px solid #4fc3f7;">
                            تواصل معنا
                        </a>
                    </div>
                </div>
    
                <!-- الجزء البصري -->
                <div style="position: relative;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(10px); border-radius: 20px; padding: 40px; border: 1px solid rgba(255,255,255,0.2); box-shadow: 0 20px 40px rgba(0,0,0,0.2);">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 30px;">
                            <div style="background: rgba(79,195,247,0.2); border-radius: 12px; padding: 20px; text-align: center; border: 1px solid rgba(79,195,247,0.3);">
                                <div style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">2015</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">سنة التأسيس</div>
                            </div>
                            <div style="background: rgba(79,195,247,0.2); border-radius: 12px; padding: 20px; text-align: center; border: 1px solid rgba(79,195,247,0.3);">
                                <div style="font-size: 2.5rem; font-weight: 700; color: #4fc3f7; margin-bottom: 5px;">98%</div>
                                <div style="font-size: 0.9rem; opacity: 0.8;">رضا العملاء</div>
                            </div>
                        </div>
    
                        <div style="height: 200px; background: rgba(0,0,0,0.3); border-radius: 12px; display: flex; align-items: center; justify-content: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, transparent 60%, rgba(79,195,247,0.1) 100%);"></div>
                            <div style="position: relative; z-index: 2; text-align: center; padding: 20px;">
                                <div style="font-size: 1.8rem; color: #4fc3f7; margin-bottom: 10px;">📊 +4000 نقطة</div>
                                <div style="font-size: 1rem; opacity: 0.8;">متوسط أرباح شهرياً</div>
                            </div>
                        </div>
                    </div>
    
                    <div style="position: absolute; top: -20px; left: -20px; width: 100%; height: 100%; border: 2px solid rgba(79,195,247,0.3); border-radius: 20px; z-index: -1;"></div>
                </div>
            </div>
        </div>
    
        <!-- أنيميشن الكتابة -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const texts = [
                    {text: "فريق ", bold: "صلاح تريدنج", rest: "، رواد في تقديم إشارات التداول الدقيقة للذهب والعملات الرقمية منذ 2015."},
                    {text: "نعمل بمنهجية علمية صارمة في ", bold: "إدارة المخاطر", rest: "، مع نتائج مثبتة تصل إلى ", bold2: "+4000 نقطة", rest2: " شهريًا."},
                    {text: "نقدم ", bold: "متابعة لحظية", rest: " واستراتيجيات مدروسة لتحقيق ", bold2: "أعلى العوائد", rest2: " بأقل المخاطر."}
                ];
                
                const speed = 25;
                const lineElements = [
                    document.getElementById('typing-line1'),
                    document.getElementById('typing-line2'),
                    document.getElementById('typing-line3')
                ];
                
                async function typeWriter() {
                    for (let i = 0; i < texts.length; i++) {
                        const line = texts[i];
                        let fullText = line.text;
                        
                        if (line.bold) {
                            fullText += `<strong style="color:#4fc3f7">${line.bold}</strong>`;
                        }
                        
                        fullText += line.rest || '';
                        
                        if (line.bold2) {
                            fullText += `<strong style="color:#4fc3f7">${line.bold2}</strong>`;
                        }
                        
                        fullText += line.rest2 || '';
                        
                        await typeText(lineElements[i], fullText);
                        
                        if (i < texts.length - 1) {
                            lineElements[i].style.borderRight = 'none';
                            lineElements[i+1].style.borderRight = '2px solid #4fc3f7';
                        } else {
                            lineElements[i].style.borderRight = 'none';
                        }
                        await new Promise(resolve => setTimeout(resolve, 300));
                    }
                }
                
                function typeText(element, html) {
                    return new Promise(resolve => {
                        element.innerHTML = '';
                        let charIndex = 0;
                        const typingInterval = setInterval(() => {
                            if (charIndex < html.length) {
                                element.innerHTML = html.substring(0, charIndex + 1);
                                charIndex++;
                            } else {
                                clearInterval(typingInterval);
                                resolve();
                            }
                        }, speed);
                    });
                }
                
                setTimeout(typeWriter, 800);
            });
            </script>
    
        <!-- الأنيميشنز -->
        <style>
            @keyframes underlineGrow {
                0% { width: 0; right: 0; }
                100% { width: 100%; right: 0; }
            }
            @keyframes blinkCursor {
                0%, 100% { border-right-color: #4fc3f7; }
                50% { border-right-color: transparent; }
            }
            .cta-button:hover {
                background: linear-gradient(45deg, #2a89d8, #4fc3f7) !important;
                transform: translateY(-3px);
                box-shadow: 0 10px 20px rgba(42, 137, 216, 0.5) !important;
            }
            .secondary-button:hover {
                background: rgba(79,195,247,0.1) !important;
                transform: translateY(-3px);
            }
            #about div[style*="background: rgba(79,195,247,0.2)"]:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 20px rgba(0,0,0,0.3) !important;
            }
            @media (max-width: 992px) {
                #about > div > div {
                    grid-template-columns: 1fr !important;
                }
                #about .typing-container {
                    margin-bottom: 40px;
                }
            }
        </style>
    </section>

    <section id="client-results" style="position: relative; padding: 100px 0; background: linear-gradient(to bottom, #0a192f, #0f2a4a); font-family: 'Tajawal', sans-serif; color: #fff;">

        <!-- تأثيرات خلفية ديناميكية -->
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: 
            radial-gradient(circle at 20% 30%, rgba(79, 195, 247, 0.1) 0%, transparent 40%),
            radial-gradient(circle at 80% 70%, rgba(42, 137, 216, 0.1) 0%, transparent 40%);
            z-index: 0;"></div>
    
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 20px; position: relative; z-index: 1;">
    
            <!-- العنوان الرئيسي -->
            <div style="text-align: center; margin-bottom: 60px;">
                <h2 style="font-size: 2.8rem; font-weight: 700; margin-bottom: 20px; color: #4fc3f7; position: relative; display: inline-block;">
                    <span style="position: relative; z-index: 2;">شهادات عملائنا</span>
                    <span style="position: absolute; bottom: 5px; right: 0; width: 100%; height: 12px; background: rgba(79, 195, 247, 0.3); z-index: 1;"></span>
                </h2>
                <p style="font-size: 1.2rem; color: rgba(255,255,255,0.7); max-width: 700px; margin: 0 auto;">
                    صور حقيقية وتجارب ناجحة لعملائنا الذين حققوا نتائج مذهلة معنا
                </p>
            </div>
    
            <!-- شبكة العملاء -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 30px;">
    
                <!-- عميل 1 -->
                <div style="background: rgba(255,255,255,0.05); backdrop-filter: blur(10px); border-radius: 15px; overflow: hidden; border: 1px solid rgba(255,255,255,0.1); transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <div style="display: flex; align-items: center; padding: 25px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                        <div style="width: 80px; height: 80px; border-radius: 50%; overflow: hidden; margin-left: 20px; border: 2px solid #4fc3f7;">
                            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="محمد أحمد" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div>
                            <h3 style="font-size: 1.4rem; margin: 0 0 5px 0; color: #fff;">محمد أحمد</h3>
                            <div style="display: flex; align-items: center;">
                                <span style="color: #00ffaa; font-weight: 600; margin-left: 10px;">+ $2,450</span>
                                <span style="background: rgba(79,195,247,0.2); color: #4fc3f7; padding: 3px 10px; border-radius: 20px; font-size: 0.8rem;">الذهب</span>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 25px;">
                        <div style="height: 200px; background: #112240; border-radius: 10px; overflow: hidden; margin-bottom: 20px; position: relative;">
                            <img src="1.png" alt="نتيجة التداول" style="width: 100%; height: 100%; object-fit: cover;">
                            <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: #fff; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">
                                صورة حقيقية من حساب العميل
                            </div>
                        </div>
                        <p style="color: rgba(255,255,255,0.8); line-height: 1.7; font-size: 1rem; margin-bottom: 20px;">
                            "بفضل التحليلات الدقيقة، تمكنت من تحقيق أرباح ممتازة في فترة قصيرة. فريق الدعم كان متاحاً دائماً للإجابة على استفساراتي."
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: rgba(255,255,255,0.6); font-size: 0.9rem;">يناير 2023</span>
                            <div style="display: flex;">
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                            </div>
                        </div>
                    </div>
                </div>
    
                <!-- عميل 2 -->
                <div style="background: rgba(255,255,255,0.05); backdrop-filter: blur(10px); border-radius: 15px; overflow: hidden; border: 1px solid rgba(255,255,255,0.1); transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <div style="display: flex; align-items: center; padding: 25px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                        <div style="width: 80px; height: 80px; border-radius: 50%; overflow: hidden; margin-left: 20px; border: 2px solid #4fc3f7;">
                            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="نورا محمد" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div>
                            <h3 style="font-size: 1.4rem; margin: 0 0 5px 0; color: #fff;">نورا محمد</h3>
                            <div style="display: flex; align-items: center;">
                                <span style="color: #00ffaa; font-weight: 600; margin-left: 10px;">+ $1,870</span>
                                <span style="background: rgba(79,195,247,0.2); color: #4fc3f7; padding: 3px 10px; border-radius: 20px; font-size: 0.8rem;">العملات</span>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 25px;">
                        <div style="height: 200px; background: #112240; border-radius: 10px; overflow: hidden; margin-bottom: 20px; position: relative;">
                            <img src="https://i.imgur.com/pLQxW9k.png" alt="نتيجة التداول" style="width: 100%; height: 100%; object-fit: cover;">
                            <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: #fff; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">
                                صورة حقيقية من حساب العميل
                            </div>
                        </div>
                        <p style="color: rgba(255,255,255,0.8); line-height: 1.7; font-size: 1rem; margin-bottom: 20px;">
                            "الاستراتيجيات المدروسة ساعدتني في تحقيق أرباح ثابتة ومستمرة. لم أتوقع أن أحقق كل هذه الأرباح في أول تجربة لي."
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: rgba(255,255,255,0.6); font-size: 0.9rem;">مارس 2023</span>
                            <div style="display: flex;">
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                            </div>
                        </div>
                    </div>
                </div>
    
                <!-- عميل 3 -->
                <div style="background: rgba(255,255,255,0.05); backdrop-filter: blur(10px); border-radius: 15px; overflow: hidden; border: 1px solid rgba(255,255,255,0.1); transition: all 0.3s ease; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">
                    <div style="display: flex; align-items: center; padding: 25px; border-bottom: 1px solid rgba(255,255,255,0.1);">
                        <div style="width: 80px; height: 80px; border-radius: 50%; overflow: hidden; margin-left: 20px; border: 2px solid #4fc3f7;">
                            <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="خالد سعيد" style="width: 100%; height: 100%; object-fit: cover;">
                        </div>
                        <div>
                            <h3 style="font-size: 1.4rem; margin: 0 0 5px 0; color: #fff;">خالد سعيد</h3>
                            <div style="display: flex; align-items: center;">
                                <span style="color: #00ffaa; font-weight: 600; margin-left: 10px;">+ $3,200</span>
                                <span style="background: rgba(79,195,247,0.2); color: #4fc3f7; padding: 3px 10px; border-radius: 20px; font-size: 0.8rem;">المؤشرات</span>
                            </div>
                        </div>
                    </div>
                    <div style="padding: 25px;">
                        <div style="height: 200px; background: #112240; border-radius: 10px; overflow: hidden; margin-bottom: 20px; position: relative;">
                            <img src="https://i.imgur.com/QZwVX9z.png" alt="نتيجة التداول" style="width: 100%; height: 100%; object-fit: cover;">
                            <div style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.7); color: #fff; padding: 5px 15px; border-radius: 20px; font-size: 0.9rem;">
                                صورة حقيقية من حساب العميل
                            </div>
                        </div>
                        <p style="color: rgba(255,255,255,0.8); line-height: 1.7; font-size: 1rem; margin-bottom: 20px;">
                            "الدقة في التحليل والمتابعة المستمرة كانت سبب نجاحي في تحقيق هذه الأرباح. أنصح الجميع بالتجربة."
                        </p>
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="color: rgba(255,255,255,0.6); font-size: 0.9rem;">مايو 2023</span>
                            <div style="display: flex;">
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                                <span style="color: #ffc107; margin-left: 5px;">★</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    
            <!-- زر التحفيز -->
            <div style="text-align: center; margin-top: 60px;">
                <a href="#" style="display: inline-flex; align-items: center; background: linear-gradient(45deg, #4fc3f7, #2a89d8); color: white; padding: 16px 45px; border-radius: 50px; font-size: 1.2rem; font-weight: 600; text-decoration: none; transition: all 0.3s ease; box-shadow: 0 10px 25px rgba(42, 137, 216, 0.4);">
                    <span>كن التالي في قائمة الناجحين</span>
                    <span style="margin-right: 15px; font-size: 1.3rem;">🔥</span>
                </a>
            </div>
        </div>
    
        <!-- تأثيرات الجافاسكريبت -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // تأثير تحويل البطاقات عند التحويم
                const cards = document.querySelectorAll('#client-results > div > div > div');
                
                cards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.transform = 'translateY(-10px)';
                        this.style.boxShadow = '0 15px 40px rgba(0,0,0,0.4)';
                        this.style.borderColor = 'rgba(79,195,247,0.3)';
                    });
                    
                    card.addEventListener('mouseleave', function() {
                        this.style.transform = '';
                        this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.2)';
                        this.style.borderColor = 'rgba(255,255,255,0.1)';
                    });
                });
                
                // تأثير زر التحفيز
                const ctaButton = document.querySelector('#client-results a[href="#"]');
                
                ctaButton.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                    this.style.boxShadow = '0 15px 35px rgba(42, 137, 216, 0.6)';
                });
                
                ctaButton.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '0 10px 25px rgba(42, 137, 216, 0.4)';
                });
            });
        </script>
    
        <!-- تأثيرات CSS الإضافية -->
        <style>
            @media (max-width: 768px) {
                #client-results {
                    padding: 60px 0;
                }
                
                #client-results > div > div {
                    grid-template-columns: 1fr !important;
                }
                
                #client-results h2 {
                    font-size: 2.2rem !important;
                }
            }
        </style>
    </section>

    <!-- قسم الإشارات -->
    <section class="signals" id="signals">
        <h2 class="section-title">إشارات التداول</h2>
        <p style="text-align: center; color: var(--text-secondary); max-width: 800px; margin: 0 auto 50px;">
            اختر الباقة المناسبة لك واستفد من إشاراتنا الاحترافية ذات الدقة العالية
        </p>
        <div class="pricing-cards">
            <div class="pricing-card">
                <h3>الباقة الشهرية</h3>
                <div class="price">65$ <span class="original-price">75$</span></div>
                <ul class="pricing-features">
                    <li>إشارات احترافية مدروسة بدقة</li>
                    <li>نشتغل على Risk / Reward عالي</li>
                    <li>تسوب صغير جدًا (30 نقطة فقط)</li>
                    <li>متابعة لحظية</li>
                    <li>5-7 إشارات يومياً</li>
                    <li>دقة تتجاوز 85%</li>
                </ul>
                <a href="#" class="pricing-button">اشترك الآن</a>
            </div>
            <div class="pricing-card">
                <h3>3 شهور</h3>
                <div class="price">100$ <span class="original-price">200$</span></div>
                <ul class="pricing-features">
                    <li>كل مميزات الباقة الشهرية</li>
                    <li>توفير 50%</li>
                    <li>دعم أولوية</li>
                    <li>تحليلات إضافية</li>
                    <li>إشارات VIP خاصة</li>
                    <li>توصيات خاصة بالعملات</li>
                </ul>
                <a href="#" class="pricing-button">اشترك الآن</a>
            </div>
            <div class="pricing-card">
                <h3>مدى الحياة</h3>
                <div class="price">199$ <span class="original-price">400$</span></div>
                <ul class="pricing-features">
                    <li>كل مميزات الباقات الأخرى</li>
                    <li>اشتراك مدى الحياة</li>
                    <li>دعم فوري 24/7</li>
                    <li>وصول لكل القنوات</li>
                    <li>إشارات ذهبية حصرية</li>
                    <li>تحليلات متقدمة يومية</li>
                </ul>
                <a href="#" class="pricing-button">اشترك الآن</a>
            </div>
        </div>
    </section>

    <!-- قسم العملاء -->
    <section class="testimonials" id="testimonials">
        <h2 class="section-title">آراء عملائنا</h2>
        <p style="text-align: center; color: var(--text-secondary); max-width: 800px; margin: 0 auto 50px;">
            ثقة أكثر من 500 مشترك وتقييمات عالية من أشخاص شهدوا نتائج حقيقية
        </p>
        
        <div class="testimonial-slider">
            <div class="testimonial-slide">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="عميل" class="client-avatar">
                <div class="client-rating">★★★★★</div>
                <p class="client-text">"منذ أن انضممت إلى صلاح تريدنج، تغيرت حياتي المالية تمامًا. الإشارات دقيقة والربح مضمون. أنصح الجميع بالاشتراك معهم لأنهم محترفون بمعنى الكلمة."</p>
                <div class="client-name">أحمد السيد</div>
            </div>
            
            <div class="testimonial-slide">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="عميلة" class="client-avatar">
                <div class="client-rating">★★★★★</div>
                <p class="client-text">"كنت أخسر باستمرار في التداول حتى تعرفت على صلاح تريدنج. الآن أرباحي ثابتة ومتزايدة. فريق الدعم رائع ويقدمون المساعدة في أي وقت."</p>
                <div class="client-name">سارة محمد</div>
            </div>
            
            <div class="testimonial-slide">
                <img src="https://randomuser.me/api/portraits/men/75.jpg" alt="عميل" class="client-avatar">
                <div class="client-rating">★★★★☆</div>
                <p class="client-text">"إشارات دقيقة جداً ومبنية على تحليل صحيح. حققت في أول شهر 1200$ ربح صافي. أنصح بالمتابعة اليومية والالتزام بالإشارات."</p>
                <div class="client-name">خالد عبدالله</div>
            </div>
        </div>
    </section>

    <!-- قسم الأسئلة الشائعة -->
    <section class="faq" id="faq">
        <h2 class="section-title">الأسئلة الشائعة</h2>
        <p style="text-align: center; color: var(--text-secondary); max-width: 800px; margin: 0 auto 50px;">
            أجوبة على أكثر الأسئلة التي تردنا من العملاء الجدد
        </p>
        
        <div class="faq-container">
            <div class="faq-item">
                <div class="faq-question">ما هي نسبة دقة الإشارات؟</div>
                <div class="faq-answer">
                    <p>نسبة دقة إشاراتنا تتجاوز 85% في المتوسط، وذلك بفضل التحليل الفني الدقيق وإدارة المخاطر المحكمة. نحرص على إرسال الإشارات فقط عندما تكون احتمالية النجاح عالية جداً.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">كم عدد الإشارات التي ترسلونها يومياً؟</div>
                <div class="faq-answer">
                    <p>نرسل بين 5-7 إشارات يومياً في المتوسط، وقد يزيد أو يقل العدد حسب ظروف السوق. نفضل الجودة على الكمية، لذلك لا نرسل إشارات إلا إذا كانت ذات فرصة نجاح عالية.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">هل تقدمون ضماناً للربح؟</div>
                <div class="faq-answer">
                    <p>لا يمكن لأي شخص أن يقدم ضماناً للربح في التداول، ولكننا نضمن لك أننا نقدم أفضل تحليل ممكن ونتبع استراتيجيات مجربة لزيادة احتمالية الربح وتقليل المخاطر.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">كيف يتم إرسال الإشارات؟</div>
                <div class="faq-answer">
                    <p>نرسل الإشارات عبر قناة التليجرام الخاصة بنا، مع شرح كامل لكل إشارة وتحديد نقاط الدخول والخروج وأهداف الربح وإيقاف الخسارة.</p>
                </div>
            </div>
            
            <div class="faq-item">
                <div class="faq-question">هل تناسب الإشارات المبتدئين؟</div>
                <div class="faq-answer">
                    <p>نعم، إشاراتنا مناسبة للمبتدئين والمحترفين. نوضح كل شيء بالتفصيل ويمكنك التواصل معنا لأي استفسار. كما نقدم نصائح وإرشادات للمبتدئين.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- قسم التواصل -->
    <section class="contact" id="contact">
        <div class="contact-container">
            <h2>تواصل معنا</h2>
            <p style="text-align: center; color: var(--text-secondary); margin-bottom: 30px;">
                لديك استفسار أو ترغب في معرفة المزيد عن خدماتنا؟ تواصل معنا مباشرة
            </p>
            
            <form class="contact-form">
                <div class="form-group">
                    <label for="name">الاسم</label>
                    <input type="text" id="name" required>
                </div>
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" required>
                </div>
                <div class="form-group">
                    <label for="message">الرسالة</label>
                    <textarea id="message" rows="5" required></textarea>
                </div>
                <button type="submit" class="submit-button">إرسال الرسالة</button>
            </form>
            
            <div class="social-links">
                <a href="https://t.me/salah4w" class="social-link" target="_blank">
                    <i class="fab fa-telegram social-icon"></i>
                    <span>تيليجرام: @salah4w</span>
                </a>
                <a href="https://instagram.com/salah4w" class="social-link" target="_blank">
                    <i class="fab fa-instagram social-icon"></i>
                    <span>إنستجرام: @salah4w</span>
                </a>
            </div>
        </div>
    </section>

    <!-- الفوتر -->
    <footer>
        <div class="footer-container">
            <div class="footer-col">
                <h3>صلاح تريدنج</h3>
                <p style="color: var(--text-secondary); line-height: 1.6;">وجهتك الأولى للتداول المحترف والربح المضمون بإذن الله. نقدم إشارات دقيقة واستراتيجيات مجربة.</p>
            </div>
            
            <div class="footer-col">
                <h3>روابط سريعة</h3>
                <ul class="footer-links">
                    <li><a href="#home">الرئيسية</a></li>
                    <li><a href="#about">من نحن</a></li>
                    <li><a href="#results">أرباح العملاء</a></li>
                    <li><a href="#signals">إشارات التداول</a></li>
                    <li><a href="#testimonials">آراء العملاء</a></li>
                    <li><a href="#contact">تواصل معنا</a></li>
                </ul>
            </div>
            
           
            
            <div class="footer-col">
                <h3>وسائل التواصل</h3>
                <ul class="footer-links">
                    <li><a href="https://t.me/salah4w" target="_blank"><i class="fab fa-telegram"></i> تيليجرام</a></li>
                    <li><a href="https://instagram.com/salah4w" target="_blank"><i class="fab fa-instagram"></i> إنستجرام</a></li>
                    <li><a href="mailto:<EMAIL>"><i class="fas fa-envelope"></i> البريد الإلكتروني</a></li>
                    <li><a href="#"><i class="fas fa-phone"></i> الدعم الفني</a></li>
                </ul>
            </div>
        </div>
        
        <div class="copyright">
            © 2023 صلاح تريدنج. جميع الحقوق محفوظة.
        </div>
    </footer>

    <!-- Font Awesome للأيقونات -->
    <script src="https://kit.fontawesome.com/a076d05399.js" crossorigin="anonymous"></script>
    
    <!-- Scripts -->
    <script>
        // تفعيل أنيميشن التمرير للبطاقات
        document.addEventListener('DOMContentLoaded', function() {
            const animateElements = document.querySelectorAll('.pricing-card, .screenshot-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, { threshold: 0.1 });
            
            animateElements.forEach(element => {
                observer.observe(element);
            });
            
            // FAQ Accordion
            const faqItems = document.querySelectorAll('.faq-item');
            faqItems.forEach(item => {
                const question = item.querySelector('.faq-question');
                question.addEventListener('click', () => {
                    // Close all other items
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                        }
                    });
                    
                    // Toggle current item
                    item.classList.toggle('active');
                });
            });
            
            // Simple testimonial slider
            let currentTestimonial = 0;
            const testimonials = document.querySelectorAll('.testimonial-slide');
            
            function showTestimonial(index) {
                testimonials.forEach((testimonial, i) => {
                    testimonial.style.display = i === index ? 'block' : 'none';
                });
            }
            
            // Initialize
            showTestimonial(currentTestimonial);
            
            // Auto slide
            setInterval(() => {
                currentTestimonial = (currentTestimonial + 1) % testimonials.length;
                showTestimonial(currentTestimonial);
            }, 5000);
        });
    </script>
</body>
</html>